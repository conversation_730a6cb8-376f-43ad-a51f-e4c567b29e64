<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;

/**
 * ReportsMainController
 *
 * Main controller for reports system - handles exercise listing, dashboard, and navigation
 */
class ReportsMainController extends Controller
{
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionsModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
    }

    /**
     * [GET] Main reports index - redirects to exercises list
     * URI: /reports
     */
    public function index()
    {
        return redirect()->to('reports/exercises');
    }

    /**
     * [GET] Display exercises for reports (all statuses)
     * URI: /reports/exercises
     */
    public function exercises()
    {
        // Check if user is logged in and has admin role
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            // Set flash data for popup message
            session()->setFlashdata('swal_icon', 'info');
            session()->setFlashdata('swal_title', 'System Update');
            session()->setFlashdata('swal_text', 'Reports Feature System update is in progress. Check again later');

            // Redirect back to dashboard
            return redirect()->to('dashboard');
        }

        try {
            // 1. Get exercises filtered by organization
            $orgId = session()->get('org_id');
            log_message('info', 'ReportsMainController::exercises - Organization ID from session: ' . ($orgId ?: 'null'));

            if ($orgId) {
                $exercises = $this->exerciseModel->where('org_id', $orgId)->where('deleted_at IS NULL')->orderBy('created_at', 'DESC')->findAll();
                log_message('info', 'ReportsMainController::exercises - Found ' . count($exercises) . ' exercises for org_id: ' . $orgId);
            } else {
                $exercises = $this->exerciseModel->where('deleted_at IS NULL')->orderBy('created_at', 'DESC')->findAll();
                log_message('info', 'ReportsMainController::exercises - No org_id in session, showing all ' . count($exercises) . ' exercises');
            }

            // 2. Add statistics for each exercise using correct relationships
            $positionsGroupModel = new \App\Models\PositionsGroupModel();

            foreach ($exercises as &$exercise) {
                // Count positions for this exercise through positions_groups
                $exercise['position_count'] = $this->positionsModel
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
                    ->where('positions_groups.exercise_id', $exercise['id'])
                    ->where('positions.deleted_at IS NULL')
                    ->countAllResults();

                // Count applications for this exercise from AppxApplicationDetailsModel
                $exercise['application_count'] = $this->applicationModel
                    ->where('exercise_id', $exercise['id'])
                    ->countAllResults();
            }

            $data = [
                'title' => 'Reports - Exercises',
                'menu' => 'reports',
                'exercises' => $exercises
            ];

            return view('application_reports/appx_reports_exercises', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercises for reports: ' . $e->getMessage());

            $data = [
                'title' => 'Reports - Exercises',
                'menu' => 'reports',
                'exercises' => []
            ];

            return view('application_reports/appx_reports_exercises', $data);
        }
    }

    /**
     * [GET] Display reports dashboard for specific exercise
     * URI: /reports/dashboard/{exerciseId}
     */
    public function dashboard($exerciseId)
    {
        try {
            // Get exercise from ExerciseModel
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Check if user has access to this exercise (organization validation)
            $orgId = session()->get('org_id');
            if ($orgId && $exercise['org_id'] != $orgId) {
                log_message('warning', 'User attempted to access exercise from different organization. User org_id: ' . $orgId . ', Exercise org_id: ' . $exercise['org_id']);
                return redirect()->to('reports/exercises')
                                ->with('error', 'Access denied: Exercise belongs to different organization');
            }

            // Get statistics using correct relationships
            $statistics = [
                'total_applications' => $this->applicationModel->where('exercise_id', $exerciseId)->countAllResults(),
                'total_positions' => $this->positionsModel
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
                    ->where('positions_groups.exercise_id', $exerciseId)
                    ->where('positions.deleted_at IS NULL')
                    ->countAllResults(),
                'pre_screened_passed' => $this->applicationModel->where('exercise_id', $exerciseId)->where('pre_screened_status', 'passed')->countAllResults(),
                'pre_screened_failed' => $this->applicationModel->where('exercise_id', $exerciseId)->where('pre_screened_status', 'failed')->countAllResults(),
                'shortlisted' => $this->applicationModel->where('exercise_id', $exerciseId)->where('shortlist_status', 'shortlisted')->countAllResults(),
                'interviewed' => $this->applicationModel->where('exercise_id', $exerciseId)->where('interviewed', 1)->countAllResults(),
                'selected' => $this->applicationModel->where('exercise_id', $exerciseId)->where('selected_status', 'selected')->countAllResults()
            ];

            $data = [
                'title' => 'Reports Dashboard - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_dashboard', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercise dashboard data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading exercise dashboard');
        }
    }

    /**
     * [GET] Display positions for exercise reports
     * URI: /reports/positions/{exerciseId}
     */
    public function positions($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Check if user has access to this exercise (organization validation)
            $orgId = session()->get('org_id');
            if ($orgId && $exercise['org_id'] != $orgId) {
                log_message('warning', 'User attempted to access exercise positions from different organization. User org_id: ' . $orgId . ', Exercise org_id: ' . $exercise['org_id']);
                return redirect()->to('reports/exercises')
                                ->with('error', 'Access denied: Exercise belongs to different organization');
            }

            // Get positions with statistics
            $positions = $this->exerciseModel->getPositionsWithStatistics($exerciseId);

            $data = [
                'title' => 'Reports - Positions',
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions for reports: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions data');
        }
    }

    /**
     * [GET] Display positions report for specific exercise
     * URI: /reports/positions-report/{exerciseId}
     */
    public function positionsReport($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get all positions for this exercise with group details
            $positions = $this->positionsModel->getPositionsByExerciseId($exerciseId);

            $data = [
                'title' => 'Positions Report - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions_report', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions report data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions report');
        }
    }

}
